<template>
  <div id="app">
    <vue2-water-marker
      :key="watermarkKey"
      style="position: fixed"
      :text="text"
      opacity="0.2">
    </vue2-water-marker>
    <router-view></router-view>
  </div>
</template>

<script>
import {mapGetters} from 'vuex';
import {profile} from '@/api/login';
import store from "@/store"

export default {
  components: {},
  name: 'App',
  computed: {
    ...mapGetters(['token', 'role', 'userInfo', 'name']),
    currentRole() {
      // 确保响应式地更新水印文本
      if (store.getters.token && typeof store.getters.token == 'string' && this.role) {
        if (this.role.role_type == 'stu') {
          this.text = this.name || '';
        } else {
          // 检查 userInfo 和 phone 是否存在
          const phone = this.userInfo && this.userInfo.phone ? this.userInfo.phone : '';
          const displayName = this.name || '';
          this.text = displayName + (phone ? ',' + this.splitNumber(phone) : '');
        }
      } else {
        this.text = "";
      }
      return this.role;
    }
  },
  watch: {
    currentRole() {
      // 当角色信息变化时，强制重新渲染水印
      this.refreshWatermark();
    },
    '$route'() {
      // 当路由变化时，延迟刷新水印以确保数据已更新
      this.$nextTick(() => {
        setTimeout(() => {
          this.refreshWatermark();
        }, 100);
      });
    },
    'userInfo': {
      handler() {
        // 当用户信息变化时，刷新水印
        this.refreshWatermark();
      },
      deep: true
    }
  },
  data() {
    return {
      text: '',
      watermarkKey: 0
    }
  },
  async created() {
    let that = this;
    this.getConfigKey("sys.service.type").then(response => {
      that.$store.dispatch('app/SetSysType',response.msg);
    });
    this.timer = setInterval(() => {
      that.initToken();
      clearInterval(that.timer);
    }, 50);
    //首次进入检查是否登录过期
  },
  methods: {
    refreshWatermark() {
      // 强制重新渲染水印组件
      this.watermarkKey++;
    },
    splitNumber(val) {
      // 检查参数是否为有效的字符串且长度足够
      if (!val || typeof val !== 'string' || val.length < 8) {
        return val || ''; // 如果参数无效，返回原值或空字符串
      }
      return val.substr(0, 3) + '****' + val.substr(7)
    },
    initToken() {
      let url = this.$route.path;
      if ("/bind" == url) {//家长绑定页面，不需登录

      } else if ("/profile" == url) {
        let quToken = this.$route.query.token;
        if (!_.isEmpty(quToken)) {
          // profile();
          this.$store
              .dispatch('loginTri', quToken)
              .then(() => {
                // this.$modal.msgSuccess('登录成功');
                //获取用户信息
                this.$store.dispatch('GetInfo');
                //获取用户角色
                this.$store.dispatch('GetRole').then((res) => {
                  //设置第一个角色作为默认角色
                  if (res.data.length > 0) {
                    this.$store.dispatch('SetRole', res.data[0]);
                    // this.switchHandle(res.data[0]);
                    // this.initPage();
                  } else {
                    this.$modal.msgWarning('当前用户无角色，请联系管理员');
                  }
                });
                this.loading = false;
              })
              .catch(() => {
                this.loading = false;
              });
        } else {
          //this.$router.push('/');
          window.location.href = "https://cdwh.wuhousmartedu.com/whIndexNew.html";
        }
      } else {
        if (!_.isEmpty(this.token)) {
          profile();
        } else {
          // this.$router.push('/');
        }
      }
    },
    handleScroll() {

    }
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll)
  },
};
</script>

<style>
body,
html {
  font-size: 10px;
}

::-webkit-scrollbar {
  width: 5px;
  height: 10px;
}

::-webkit-scrollbar-thumb {
  background-color: #aaa;
  border-radius: 2em;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

* {
  margin: 0;
  padding: 0;
  /* font-family: PingFangSC; */
}

.el-button {
  border-color: #3888F7 !important;
  color: #3888F7 !important;
  width: 11rem;
  height: 4.2rem;
}

.el-button--primary {
  color: #fff !important;
//background-color: #3888F7 !important; //border-color: #3888F7 !important;
}

.el-button:hover {
//background-color: rgba(63, 139, 137, 0.1) !important;
  /* color: #fff !important; */
}

.el-button--primary:focus,
.el-button--primary:hover {
//background-color: #3b8989 !important; //color: #fff !important;
}

#app {
  height: 100%;
  font-family: 'Microsoft YaHei', '微软雅黑';
}
</style>
